package com.example.gymbro.features.thinkingbox.domain.mapper

import com.example.gymbro.features.thinkingbox.domain.model.events.SemanticEvent
import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * DomainMapper - 统一事件映射器
 *
 * 将SemanticEvent映射为ThinkingEvent的核心组件。
 * 支持6个统一事件：PreThinkChunk, PreThinkEnd, PhaseStart, PhaseContent, PhaseEnd, FinalArrived
 */
@Singleton
class DomainMapper @Inject constructor() {

    /**
     * 映射上下文
     */
    data class MappingContext(
        val currentPhaseId: String? = null,
        val lastPhaseId: String? = null,
        val inThinkTag: Boolean = false,
        val inThinkingTag: Boolean = false,
        val inTitleTag: Boolean = false,
        val inFinalTag: Boolean = false,
        var hasPerthinkStarted: Boolean = false,
        val perthinkCompleted: Boolean = false,
        val titleBuffer: StringBuilder = StringBuilder()
    )

    /**
     * 映射结果
     */
    data class MappingResult(
        val events: List<ThinkingEvent>,
        val context: MappingContext
    )

    /**
     * 主映射方法
     */
    fun mapSemanticToThinking(
        event: SemanticEvent,
        context: MappingContext = MappingContext()
    ): MappingResult {
        return when (event) {
            // ===== 6个统一事件系统映射 =====

            is SemanticEvent.PreThinkChunk -> {
                MappingResult(
                    events = listOf(ThinkingEvent.PreThinkChunk(event.text)),
                    context = context
                )
            }

            is SemanticEvent.PreThinkEnd -> {
                MappingResult(
                    events = listOf(ThinkingEvent.PreThinkEnd),
                    context = context.copy(
                        inThinkTag = false,
                        perthinkCompleted = true
                    )
                )
            }

            is SemanticEvent.PhaseStart -> {
                MappingResult(
                    events = listOf(ThinkingEvent.PhaseStart(event.id, event.title)),
                    context = context.copy(
                        currentPhaseId = event.id,
                        lastPhaseId = event.id,
                        hasPerthinkStarted = if (event.id == "perthink") true else context.hasPerthinkStarted
                    )
                )
            }

            is SemanticEvent.PhaseContent -> {
                MappingResult(
                    events = listOf(ThinkingEvent.PhaseContent(event.id, event.content)),
                    context = context
                )
            }

            is SemanticEvent.PhaseEnd -> {
                MappingResult(
                    events = listOf(ThinkingEvent.PhaseEnd(event.id)),
                    context = context.copy(
                        currentPhaseId = if (context.currentPhaseId == event.id) null else context.currentPhaseId
                    )
                )
            }

            is SemanticEvent.PhaseTitleUpdate -> {
                MappingResult(
                    events = listOf(ThinkingEvent.PhaseTitleUpdate(event.phaseId, event.title)),
                    context = context
                )
            }

            is SemanticEvent.FinalStart -> {
                MappingResult(
                    events = listOf(ThinkingEvent.FinalStart),
                    context = context
                )
            }

            is SemanticEvent.FinalChunk -> {
                MappingResult(
                    events = listOf(ThinkingEvent.FinalToken(event.content)),
                    context = context
                )
            }

            is SemanticEvent.FinalEnd -> {
                MappingResult(
                    events = listOf(ThinkingEvent.FinalEnd),
                    context = context
                )
            }

            is SemanticEvent.FinalArrived -> {
                // 向后兼容已弃用的事件
                @Suppress("DEPRECATION")
                MappingResult(
                    events = listOf(ThinkingEvent.FinalArrived(event.markdown)),
                    context = context
                )
            }

            // ===== 向后兼容的XML标签事件 =====

            is SemanticEvent.TagOpened -> mapTagOpened(event, context)
            is SemanticEvent.TagClosed -> mapTagClosed(event, context)
            is SemanticEvent.TextChunk -> mapTextChunk(event, context)

            // ===== 辅助事件（不需要映射）=====

            is SemanticEvent.RawThinking,
            is SemanticEvent.RawThinkingChunk,
            is SemanticEvent.RawThinkingClosed,
            is SemanticEvent.StreamFinished,
            is SemanticEvent.FunctionCallDetected,
            is SemanticEvent.TokensSnapshot,
            is SemanticEvent.ParseErrorEvent,
            -> {
                // 这些事件不需要映射到ThinkingEvent
                MappingResult(events = emptyList(), context = context)
            }
        }
    }

    /**
     * 处理标签开启事件 - 向后兼容
     */
    private fun mapTagOpened(
        event: SemanticEvent.TagOpened,
        context: MappingContext
    ): MappingResult {
        return when (event.name) {
            "think" -> {
                // 🔥 【关键修复】检测到<think>标签，创建perthink phase
                if (context.perthinkCompleted) {
                    Timber.tag("TB-MAPPER").w("⚠️ 检测到<think>标签，但perthink已完成，忽略该标签")
                    MappingResult(events = emptyList(), context = context)
                } else {
                    Timber.tag("TB-MAPPER").i("🚀 检测到<think>标签，创建perthink phase")
                    MappingResult(
                        events = listOf(ThinkingEvent.PhaseStart("perthink", "Bro is thinking")),
                        context = context.copy(
                            inThinkTag = true,
                            currentPhaseId = "perthink",
                            lastPhaseId = "perthink",
                            hasPerthinkStarted = true
                        )
                    )
                }
            }

            "final" -> {
                // 检测到<final>标签，发送FinalStart事件
                MappingResult(
                    events = listOf(ThinkingEvent.FinalStart),
                    context = context.copy(inFinalTag = true)
                )
            }
            else -> {
                // 其他标签已被StreamingThinkingMLParser处理，这里不再需要
                MappingResult(events = emptyList(), context = context)
            }
        }
    }

    /**
     * 处理标签关闭事件 - 向后兼容
     */
    private fun mapTagClosed(
        event: SemanticEvent.TagClosed,
        context: MappingContext
    ): MappingResult {
        return when (event.name) {
            "think" -> {
                Timber.tag("TB-MAPPER").d("🔚 </think>标签关闭，发送PreThinkEnd并锁定perthink状态")
                MappingResult(
                    events = listOf(ThinkingEvent.PreThinkEnd),
                    context = context.copy(
                        inThinkTag = false,
                        perthinkCompleted = true
                    )
                )
            }

            "thinking" -> {
                // </thinking>标签结束，发送ThinkingEnd事件
                MappingResult(
                    events = listOf(ThinkingEvent.ThinkingEnd),
                    context = context.copy(inThinkingTag = false)
                )
            }
            else -> {
                // 其他标签已被StreamingThinkingMLParser处理，这里不再需要
                MappingResult(events = emptyList(), context = context)
            }
        }
    }

    /**
     * 处理文本块事件
     */
    private fun mapTextChunk(
        event: SemanticEvent.TextChunk,
        context: MappingContext
    ): MappingResult {
        return when {
            // 🔥 【状态锁定修复】<think>标签内的文本内容处理
            context.inThinkTag && !context.perthinkCompleted && context.currentPhaseId == "perthink" -> {
                val events = mutableListOf<ThinkingEvent>()
                events.add(ThinkingEvent.PreThinkChunk(event.text))
                events.add(ThinkingEvent.PhaseContent("perthink", event.text))
                MappingResult(events = events, context = context)
            }

            else -> {
                MappingResult(events = emptyList(), context = context)
            }
        }
    }
}


