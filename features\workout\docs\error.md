> Task :features:workout:compileDebugKotlin FAILED
e: file:///D:/GymBro/GymBro/features/workout/src/main/kotlin/com/example/gymbro/features/workout/template/edit/TemplateEditScreen.kt:608:5 Unresolved reference 'LazyColumn'.
e: file:///D:/GymBro/GymBro/features/workout/src/main/kotlin/com/example/gymbro/features/workout/template/edit/TemplateEditScreen.kt:614:9 Unresolved reference 'items'.
e: file:///D:/GymBro/GymBro/features/workout/src/main/kotlin/com/example/gymbro/features/workout/template/edit/TemplateEditScreen.kt:616:21 Unresolved reference 'it'.
e: file:///D:/GymBro/GymBro/features/workout/src/main/kotlin/com/example/gymbro/features/workout/template/edit/TemplateEditScreen.kt:617:11 Cannot infer type for this parameter. Please specify it explicitly.
e: file:///D:/GymBro/GymBro/features/workout/src/main/kotlin/com/example/gymbro/features/workout/template/edit/TemplateEditScreen.kt:617:13 Cannot infer type for this parameter. Please specify it explicitly.
e: file:///D:/GymBro/GymBro/features/workout/src/main/kotlin/com/example/gymbro/features/workout/template/edit/TemplateEditScreen.kt:618:13 @Composable invocations can only happen from the context of a @Composable function
e: file:///D:/GymBro/GymBro/features/workout/src/main/kotlin/com/example/gymbro/features/workout/template/edit/TemplateEditScreen.kt:620:44 Unresolved reference 'versionNumber'.
e: file:///D:/GymBro/GymBro/features/workout/src/main/kotlin/com/example/gymbro/features/workout/template/edit/TemplateEditScreen.kt:622:56 Unresolved reference 'id'.

