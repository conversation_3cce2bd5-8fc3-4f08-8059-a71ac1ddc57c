package com.example.gymbro.features.thinkingbox.domain.parser

import com.example.gymbro.features.thinkingbox.domain.model.events.SemanticEvent
import com.example.gymbro.features.thinkingbox.internal.constants.ThinkingBoxStrings
import kotlinx.coroutines.flow.Flow
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * StreamingThinkingMLParser - 双时序架构XML解析器
 *
 * 🎯 核心设计目标:
 * - 严格的三态状态机：PRE_THINK → THINKING → POST_FINAL
 * - 每个XML标签都有明确的处理逻辑和UI映射
 * - 支持双时序架构：后台数据时序 + 前台UI时序分离
 * - 输出6个统一事件：PreThinkChunk, PreThinkEnd, PhaseStart, PhaseContent, PhaseEnd, FinalStart/Chunk/End
 * - 零硬编码，使用ThinkingBoxStrings常量
 */
@Singleton
class StreamingThinkingMLParser @Inject constructor(
    private val xmlScanner: XmlStreamScanner,
    private val functionCallDetector: FunctionCallDetector,
) {

    /**
     * 解析器状态枚举
     */
    enum class ParserState {
        PRE_THINK,   // <think> 预思考阶段
        THINKING,    // <thinking><phase> 正式思考阶段
        POST_FINAL   // <final> 最终输出阶段
    }

    /**
     * 解析器上下文 - 最小化状态容器
     */
    data class ParserContext(
        var state: ParserState = ParserState.PRE_THINK,
        var currentPhaseId: String? = null,
        var inTitleTag: Boolean = false,
        var titleBuffer: StringBuilder = StringBuilder(),
        var functionCallContext: FunctionCallDetector.DetectionContext? = null
    )

    /**
     * 主解析方法 - 流式XML解析
     *
     * @param messageId 消息ID，用于错误追踪
     * @param tokens 输入的token流
     * @param onEvent 语义事件回调
     */
    suspend fun parseTokenStream(
        messageId: String,
        tokens: Flow<String>,
        onEvent: suspend (SemanticEvent) -> Unit,
    ) {
        val context = ParserContext()
        context.functionCallContext = functionCallDetector.createContext()

        try {
            tokens.collect { chunk ->
                val xmlTokens = xmlScanner.feed(chunk)

                xmlTokens.forEach { token ->
                    val events = processXmlToken(token, context)
                    events.forEach { event -> onEvent(event) }
                }
            }

            // 流结束处理
            val finalEvents = handleStreamEnd(context)
            finalEvents.forEach { onEvent(it) }
        } catch (e: Exception) {
            Timber.e(e, "[$messageId] XML parsing error")
            onEvent(SemanticEvent.ParseErrorEvent(
                com.example.gymbro.features.thinkingbox.domain.model.ParseError(
                    type = com.example.gymbro.features.thinkingbox.domain.model.ErrorType.PARSING_ERROR,
                    message = e.message ?: "Unknown parsing error"
                )
            ))
        }
    }

    /**
     * 处理单个XML token，根据当前状态进行不同的处理
     */
    private fun processXmlToken(
        token: XmlStreamScanner.Token,
        context: ParserContext,
    ): List<SemanticEvent> {
        return when (context.state) {
            ParserState.PRE_THINK -> handlePreThink(token, context)
            ParserState.THINKING -> handleThinking(token, context)
            ParserState.POST_FINAL -> handlePostFinal(token, context)
        }
    }

    /**
     * 处理PRE_THINK状态的token
     * 支持标签：<think>, <thinking>, <phase>
     */
    private fun handlePreThink(
        token: XmlStreamScanner.Token,
        context: ParserContext,
    ): List<SemanticEvent> {
        return when (token) {
            is XmlStreamScanner.TagOpen -> handlePreThinkTagOpen(token, context)
            is XmlStreamScanner.TagClose -> handlePreThinkTagClose(token, context)
            is XmlStreamScanner.Text -> handlePreThinkText(token, context)
        }
    }

    /**
     * 处理PRE_THINK状态下的开始标签
     */
    private fun handlePreThinkTagOpen(
        token: XmlStreamScanner.TagOpen,
        context: ParserContext,
    ): List<SemanticEvent> {
        // 拒绝非标准标签
        if (token.name.contains(":")) {
            return emptyList()
        }

        return when (token.name.lowercase()) {
            "think" -> handleThinkTagOpen(context)
            "thinking" -> handleThinkingTagOpen(context)
            "phase" -> handlePhaseTagOpen(token, context)
            else -> emptyList()
        }
    }

    /**
     * 处理<think>标签开始
     */
    private fun handleThinkTagOpen(context: ParserContext): List<SemanticEvent> {
        // 🔥 【修复】确保状态正确设置为PRE_THINK
        context.state = ParserState.PRE_THINK
        context.currentPhaseId = "perthink"

        // 🔥 【调试】确认think标签被处理
        Timber.e("🚨 [think标签处理] <think>标签被处理，设置state=PRE_THINK, currentPhaseId=perthink")
        println("🚨 [think标签处理] <think>标签被处理，设置state=PRE_THINK, currentPhaseId=perthink")

        val event = SemanticEvent.PhaseStart("perthink", ThinkingBoxStrings.PERTHINK_TITLE)
        Timber.e("🚨 [think标签处理] 生成事件: $event")
        println("🚨 [think标签处理] 生成事件: $event")

        return listOf(event)
    }

    /**
     * 处理<thinking>标签开始
     */
    private fun handleThinkingTagOpen(context: ParserContext): List<SemanticEvent> {
        // 🔥 【关键修复】如果当前没有perthink，先创建perthink phase
        if (context.currentPhaseId == null) {
            Timber.e("🚨 [thinking标签处理] <thinking>标签开始，创建perthink phase")
            println("🚨 [thinking标签处理] <thinking>标签开始，创建perthink phase")

            context.state = ParserState.PRE_THINK
            context.currentPhaseId = "perthink"

            return listOf(
                SemanticEvent.PhaseStart("perthink", ThinkingBoxStrings.PERTHINK_TITLE)
            )
        } else {
            return finalizePerthinkAndSwitchToThinking(context)
        }
    }

    /**
     * 处理<phase>标签开始
     */
    private fun handlePhaseTagOpen(
        token: XmlStreamScanner.TagOpen,
        context: ParserContext,
    ): List<SemanticEvent> {
        val phaseId = token.attributes["id"]
        if (phaseId != null) {
            if (phaseId == "perthink") {
                // 🔥 【修复】处理<phase id="perthink">，等同于<think>标签
                context.state = ParserState.PRE_THINK
                context.currentPhaseId = "perthink"
                return listOf(
                    SemanticEvent.PhaseStart("perthink", ThinkingBoxStrings.PERTHINK_TITLE)
                )
            } else {
                // 正式phase，先结束perthink再开始新phase
                val events = finalizePerthinkAndSwitchToThinking(context).toMutableList()
                context.currentPhaseId = phaseId
                events.add(SemanticEvent.PhaseStart(phaseId, null))
                return events
            }
        }
        return emptyList()
    }

    /**
     * 处理PRE_THINK状态下的结束标签
     */
    private fun handlePreThinkTagClose(
        token: XmlStreamScanner.TagClose,
        context: ParserContext,
    ): List<SemanticEvent> {
        return when (token.name.lowercase()) {
            "think" -> handleThinkTagClose(context)
            else -> emptyList()
        }
    }

    /**
     * 处理</think>标签结束
     */
    private fun handleThinkTagClose(context: ParserContext): List<SemanticEvent> {
        if (context.currentPhaseId == "perthink") {
            context.currentPhaseId = null
            return listOf(
                SemanticEvent.PreThinkEnd,
                SemanticEvent.PhaseEnd("perthink")
            )
        }
        return emptyList()
    }

    /**
     * 处理PRE_THINK状态下的文本内容
     */
    private fun handlePreThinkText(
        token: XmlStreamScanner.Text,
        context: ParserContext,
    ): List<SemanticEvent> {
        val cleanedText = cleanPhaseHints(token.content)
        val events = mutableListOf<SemanticEvent>()

        // 🔥 【调试】记录文本处理
        Timber.e("🚨 [perthink文本处理] 原始文本: '${token.content}'")
        Timber.e("🚨 [perthink文本处理] 清理后文本: '$cleanedText'")
        Timber.e("🚨 [perthink文本处理] currentPhaseId: ${context.currentPhaseId}")
        println("🚨 [perthink文本处理] 原始文本: '${token.content}'")
        println("🚨 [perthink文本处理] 清理后文本: '$cleanedText'")
        println("🚨 [perthink文本处理] currentPhaseId: ${context.currentPhaseId}")

        if (cleanedText.isNotEmpty() && context.currentPhaseId == "perthink") {
            val preThinkChunk = SemanticEvent.PreThinkChunk(cleanedText)
            val phaseContent = SemanticEvent.PhaseContent("perthink", cleanedText)
            events.add(preThinkChunk)
            events.add(phaseContent)

            Timber.e("🚨 [perthink文本处理] 生成事件: $preThinkChunk")
            Timber.e("🚨 [perthink文本处理] 生成事件: $phaseContent")
            println("🚨 [perthink文本处理] 生成事件: $preThinkChunk")
            println("🚨 [perthink文本处理] 生成事件: $phaseContent")
        }

        // Function Call检测
        events.addAll(detectFunctionCallInText(cleanedText, context))
        return events
    }

    /**
     * 结束perthink并切换到THINKING状态
     */
    private fun finalizePerthinkAndSwitchToThinking(context: ParserContext): List<SemanticEvent> {
        val events = mutableListOf<SemanticEvent>()

        // 如果当前在perthink状态，先结束它
        if (context.currentPhaseId == "perthink") {
            events.add(SemanticEvent.PreThinkEnd)
            events.add(SemanticEvent.PhaseEnd("perthink"))
            context.currentPhaseId = null
        }

        // 切换到THINKING状态
        context.state = ParserState.THINKING
        return events
    }

    /**
     * 清洗预思考内容中的XML标签和phase提示
     */
    private fun cleanPhaseHints(text: String): String {
        return text
            .replace(Regex("<think>", RegexOption.IGNORE_CASE), "")
            .replace(Regex("</think>", RegexOption.IGNORE_CASE), "")
            .replace(Regex("<phase:[^>]*>", RegexOption.IGNORE_CASE), "")
            .replace(Regex("</phase:[^>]*>", RegexOption.IGNORE_CASE), "")
            .replace(Regex("\\bphase\\s*:\\s*[A-Za-z_]+\\b", RegexOption.IGNORE_CASE), "")
            .trim()
    }

    /**
     * 处理THINKING状态的token
     * 支持标签：<thinking>, <phase>, <title>, <final>
     */
    private fun handleThinking(
        token: XmlStreamScanner.Token,
        context: ParserContext,
    ): List<SemanticEvent> {
        return when (token) {
            is XmlStreamScanner.TagOpen -> handleThinkingTagOpen(token, context)
            is XmlStreamScanner.TagClose -> handleThinkingTagClose(token, context)
            is XmlStreamScanner.Text -> handleThinkingText(token, context)
        }
    }

    /**
     * 处理THINKING状态下的开始标签
     */
    private fun handleThinkingTagOpen(
        token: XmlStreamScanner.TagOpen,
        context: ParserContext,
    ): List<SemanticEvent> {
        // 拒绝非标准标签
        if (token.name.contains(":")) {
            return emptyList()
        }

        return when (token.name.lowercase()) {
            "thinking" -> emptyList() // thinking标签开始，无需特殊处理
            "phase" -> handlePhaseTagOpenInThinking(token, context)
            "title" -> handleTitleTagOpen(context)
            "final" -> handleFinalTagOpen(token, context)
            else -> listOf(SemanticEvent.TagOpened(token.name, token.attributes))
        }
    }

    /**
     * 处理THINKING状态下的<phase>标签
     */
    private fun handlePhaseTagOpenInThinking(
        token: XmlStreamScanner.TagOpen,
        context: ParserContext,
    ): List<SemanticEvent> {
        val phaseId = token.attributes["id"]
        if (phaseId != null && phaseId.isNotBlank()) {
            val title = token.attributes["title"]
            context.currentPhaseId = phaseId
            return listOf(SemanticEvent.PhaseStart(phaseId, title))
        }
        return emptyList()
    }

    /**
     * 处理<title>标签开始
     */
    private fun handleTitleTagOpen(context: ParserContext): List<SemanticEvent> {
        context.inTitleTag = true
        context.titleBuffer.clear()
        return emptyList()
    }

    /**
     * 处理<final>标签开始
     */
    private fun handleFinalTagOpen(
        token: XmlStreamScanner.TagOpen,
        context: ParserContext,
    ): List<SemanticEvent> {
        context.state = ParserState.POST_FINAL
        return listOf(
            SemanticEvent.TagOpened("final", token.attributes),
            SemanticEvent.FinalStart
        )
    }

    /**
     * 处理THINKING状态下的结束标签
     */
    private fun handleThinkingTagClose(
        token: XmlStreamScanner.TagClose,
        context: ParserContext,
    ): List<SemanticEvent> {
        // 拒绝非标准标签
        if (token.name.contains(":")) {
            return emptyList()
        }

        return when (token.name.lowercase()) {
            "phase" -> handlePhaseTagClose(context)
            "title" -> handleTitleTagClose(context)
            "thinking" -> handleThinkingTagCloseInThinking(context)
            else -> listOf(SemanticEvent.TagClosed(token.name))
        }
    }

    /**
     * 处理</phase>标签结束
     */
    private fun handlePhaseTagClose(context: ParserContext): List<SemanticEvent> {
        if (context.currentPhaseId != null) {
            val phaseId = context.currentPhaseId!!
            context.currentPhaseId = null
            return listOf(SemanticEvent.PhaseEnd(phaseId))
        }
        return emptyList()
    }

    /**
     * 处理</title>标签结束
     */
    private fun handleTitleTagClose(context: ParserContext): List<SemanticEvent> {
        context.inTitleTag = false
        val completeTitle = context.titleBuffer.toString()
        val phaseId = context.currentPhaseId

        if (phaseId != null && completeTitle.isNotBlank()) {
            return listOf(SemanticEvent.PhaseTitleUpdate(phaseId, completeTitle))
        }
        return emptyList()
    }

    /**
     * 处理</thinking>标签结束
     */
    private fun handleThinkingTagCloseInThinking(context: ParserContext): List<SemanticEvent> {
        val events = mutableListOf<SemanticEvent>()

        // 如果当前有激活的phase，标记数据完成
        if (context.currentPhaseId != null) {
            val phaseId = context.currentPhaseId!!
            events.add(SemanticEvent.PhaseEnd(phaseId))
        }

        // 发送TagClosed事件，标记数据流结束
        events.add(SemanticEvent.TagClosed("thinking"))

        // 切换到POST_FINAL状态，等待<final>标签
        context.state = ParserState.POST_FINAL
        return events
    }

    /**
     * 处理THINKING状态下的文本内容
     */
    private fun handleThinkingText(
        token: XmlStreamScanner.Text,
        context: ParserContext,
    ): List<SemanticEvent> {
        val events = mutableListOf<SemanticEvent>()

        if (context.inTitleTag) {
            // title标签内的文本 - 缓冲而不立即发送事件
            context.titleBuffer.append(token.content)
        } else if (context.currentPhaseId != null) {
            // phase内容文本
            events.add(SemanticEvent.PhaseContent(context.currentPhaseId!!, token.content))
        } else {
            // 兜底处理
            events.add(SemanticEvent.TextChunk(token.content))
        }

        // Function Call检测
        events.addAll(detectFunctionCallInText(token.content, context))
        return events
    }

    /**
     * 处理POST_FINAL状态的token
     * 支持标签：</final>
     */
    private fun handlePostFinal(
        token: XmlStreamScanner.Token,
        context: ParserContext,
    ): List<SemanticEvent> {
        return when (token) {
            is XmlStreamScanner.TagOpen -> emptyList() // POST_FINAL状态下忽略所有开始标签
            is XmlStreamScanner.TagClose -> handlePostFinalTagClose(token)
            is XmlStreamScanner.Text -> handlePostFinalText(token, context)
        }
    }

    /**
     * 处理POST_FINAL状态下的结束标签
     */
    private fun handlePostFinalTagClose(token: XmlStreamScanner.TagClose): List<SemanticEvent> {
        return when (token.name.lowercase()) {
            "final" -> listOf(SemanticEvent.FinalEnd)
            else -> emptyList()
        }
    }

    /**
     * 处理POST_FINAL状态下的文本内容
     */
    private fun handlePostFinalText(
        token: XmlStreamScanner.Text,
        context: ParserContext,
    ): List<SemanticEvent> {
        val events = mutableListOf<SemanticEvent>()

        if (token.content.isNotEmpty()) {
            events.add(SemanticEvent.FinalChunk(token.content))
        }

        // Function Call检测
        events.addAll(detectFunctionCallInText(token.content, context))
        return events
    }

    /**
     * 处理流结束
     */
    private fun handleStreamEnd(context: ParserContext): List<SemanticEvent> {
        val events = mutableListOf<SemanticEvent>()

        // 处理未完成的phase
        when (context.state) {
            ParserState.PRE_THINK -> {
                if (context.currentPhaseId == "perthink") {
                    events.add(SemanticEvent.PreThinkEnd)
                    events.add(SemanticEvent.PhaseEnd("perthink"))
                }
            }

            ParserState.THINKING -> {
                if (context.currentPhaseId != null) {
                    events.add(SemanticEvent.PhaseEnd(context.currentPhaseId!!))
                }
            }

            ParserState.POST_FINAL -> {
                // POST_FINAL状态下依赖流式系统处理，无需额外事件
            }
        }

        // 发送流结束事件
        events.add(SemanticEvent.StreamFinished())
        return events
    }

    /**
     * 在文本中检测Function Call
     */
    private fun detectFunctionCallInText(
        textContent: String,
        context: ParserContext,
    ): List<SemanticEvent> {
        val functionCallContext = context.functionCallContext ?: return emptyList()

        return try {
            functionCallDetector.detectInChunk(textContent, functionCallContext, "")
        } catch (e: Exception) {
            Timber.w(e, "Function call detection failed")
            emptyList()
        }
    }
}
